'use client';

import { useEffect, useState } from 'react';
import {
  Title,
  Text,
  Alert,
  LoadingOverlay,
  SimpleGrid,
  Paper,
  Group,
  ThemeIcon,
  Tabs,
  Stack,
  Badge,
  Button,
  Modal,
  TextInput,
  Select,
} from '@mantine/core';
import {
  IconAlertCircle,
  IconUsers,
  IconAddressBook,
  IconBookmark,
  IconShield,
  IconPlus,
  IconPhoto,
  IconEye,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { OwnerStats, fetchOwnerStats } from 'src/lib/ownerClient';
import { ProfilesTable } from 'src/components/Admin/ProfilesTable';
import { ContactsTable } from 'src/components/Admin/ContactsTable';
import { AssetLibrary } from 'src/components/Assets/AssetLibrary';
import { AssetCreationForm } from 'src/components/Assets/AssetCreationForm';
import { useIsOwner } from 'src/hooks/useIsOwner';
import { useRouter } from 'next/navigation';
import FullLayout from 'src/components/layouts/FullLayout';

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
}

function StatCard({ title, value, icon, color }: StatCardProps) {
  return (
    <Paper withBorder p="md" radius="md">
      <Group justify="apart">
        <div>
          <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
            {title}
          </Text>
          <Text fw={700} fz="xl">
            {value.toLocaleString()}
          </Text>
        </div>
        <ThemeIcon color={color} variant="light" size={38} radius="md">
          {icon}
        </ThemeIcon>
      </Group>
    </Paper>
  );
}

export default function OwnerPage() {
  const [stats, setStats] = useState<OwnerStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isOwner, ownedPrimaryNames, ownedPrimaryNamesWithTypes, loading: ownerLoading, error: ownerError } = useIsOwner();
  const [createModalOpened, setCreateModalOpened] = useState(false);
  const [assetModalOpened, setAssetModalOpened] = useState(false);
  const [assetRefreshTrigger, setAssetRefreshTrigger] = useState(0);
  const [contactRefreshTrigger, setContactRefreshTrigger] = useState(0);
  const [viewPublicModalOpened, setViewPublicModalOpened] = useState(false);

  // Check if owner has any dynamic TLNs (which should not show disable/delete actions)
  const hasDynamicTLN = ownedPrimaryNamesWithTypes.some(item => item.type === 'dynamic');
  const [formData, setFormData] = useState({
    username: '',
    primaryName: '',
    profileName: '',
    profileEmail: '',
  });
  const router = useRouter();

  useEffect(() => {
    // Check owner access and fetch stats
    const fetchData = async () => {
      try {
        // Check if there's an owner error first
        if (ownerError) {
          setError(ownerError);
          setLoading(false);
          return;
        }

        // Check if user is an owner
        if (!isOwner) {
          setError('Access denied - Owner privileges required');
          setLoading(false);
          return;
        }

        // Fetch stats
        const statsData = await fetchOwnerStats();
        setStats(statsData);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    // Only run if we have owner access data and not loading
    if (!ownerLoading) {
      fetchData();
    }
  }, [isOwner, ownerLoading, ownerError]);

  const handleCreateContact = () => {
    if (ownedPrimaryNames.length > 0) {
      setFormData({
        username: '',
        primaryName: ownedPrimaryNames[0],
        profileName: '',
        profileEmail: '',
      });
      setCreateModalOpened(true);
    }
  };

  const handleCreateAsset = () => {
    setAssetModalOpened(true);
  };

  const handleAssetCreated = () => {
    setAssetRefreshTrigger(prev => prev + 1);
  };

  const handleViewPublic = () => {
    setViewPublicModalOpened(true);
  };

  const handleSubmitContact = async () => {
    try {
      const contactName = `${formData.username}@${formData.primaryName}`;

      const response = await fetch('/api/admin/contacts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contactName,
          profileName: formData.profileName,
          profileEmail: formData.profileEmail,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create contact');
      }

      notifications.show({
        title: 'Success',
        message: 'Contact created successfully',
        color: 'green',
      });

      setCreateModalOpened(false);
      setFormData({
        username: '',
        primaryName: ownedPrimaryNames[0] || '',
        profileName: '',
        profileEmail: '',
      });

      // Refresh stats and contacts table
      const statsData = await fetchOwnerStats();
      setStats(statsData);
      setContactRefreshTrigger(prev => prev + 1);
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to create contact',
        color: 'red',
      });
    }
  };

  if (loading || ownerLoading) {
    return (
      <FullLayout>
        <LoadingOverlay visible />
      </FullLayout>
    );
  }

  if (error) {
    return (
      <FullLayout>
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      </FullLayout>
    );
  }

  if (!isOwner) {
    return (
      <FullLayout>
        <Alert icon={<IconAlertCircle size={16} />} title="Access Denied" color="red">
          You don't have owner privileges for any primary names.
        </Alert>
      </FullLayout>
    );
  }

  return (
    <FullLayout>
      <Stack gap="xl">
        {/* Header */}
        <Group justify="space-between">
          <Group>
            <ThemeIcon size={40} radius="md" color="blue">
              <IconShield size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Owner Dashboard</Title>
              <Text c="dimmed">
                Manage your namespace: {ownedPrimaryNames.join(', ')}
              </Text>
              <Group gap="xs" mt="xs">
                {ownedPrimaryNames.map((name) => (
                  <Badge key={name} variant="light" color="blue">
                    {name}
                  </Badge>
                ))}
              </Group>
            </div>
          </Group>
          <Group>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={handleCreateContact}
            >
              Create Contact
            </Button>
            <Button
              leftSection={<IconPhoto size={16} />}
              onClick={handleCreateAsset}
              variant="outline"
            >
              Create Asset
            </Button>
            <Button
              leftSection={<IconEye size={16} />}
              onClick={handleViewPublic}
              variant="light"
            >
              View Public Assets
            </Button>
            <Text size="sm" c="dimmed">
              Owner Access
            </Text>
          </Group>
        </Group>

        {/* Statistics Cards */}
        {stats && (
          <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="md">
            <StatCard
              title="Profiles in Namespace"
              value={stats.profiles}
              icon={<IconUsers size={18} />}
              color="blue"
            />
            <StatCard
              title="Contacts in Namespace"
              value={stats.contacts}
              icon={<IconAddressBook size={18} />}
              color="green"
            />
            <StatCard
              title="Bookmarks in Namespace"
              value={stats.bookmarks}
              icon={<IconBookmark size={18} />}
              color="orange"
            />
          </SimpleGrid>
        )}

        {/* Data Tables */}
        <Tabs defaultValue="contacts" variant="outline">
          <Tabs.List>
            <Tabs.Tab value="contacts" leftSection={<IconAddressBook size={16} />}>
              Contacts
            </Tabs.Tab>
            <Tabs.Tab value="profiles" leftSection={<IconUsers size={16} />}>
              Profiles
            </Tabs.Tab>
            <Tabs.Tab value="assets" leftSection={<IconPhoto size={16} />}>
              Assets
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="contacts" pt="md">
            <ContactsTable showActions={true} checkOwnership={true} refreshTrigger={contactRefreshTrigger} />
          </Tabs.Panel>

          <Tabs.Panel value="profiles" pt="md">
            <ProfilesTable showActions={false} />
          </Tabs.Panel>

          <Tabs.Panel value="assets" pt="md">
            <AssetLibrary refreshTrigger={assetRefreshTrigger} />
          </Tabs.Panel>
        </Tabs>

        {/* Create Contact Modal */}
        <Modal
          opened={createModalOpened}
          onClose={() => setCreateModalOpened(false)}
          title="Create New Contact"
          centered
        >
          <Stack gap="md">
            <Group grow>
              <TextInput
                label="Username"
                placeholder="Enter username"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                required
              />
              <Select
                label="Primary Name"
                data={ownedPrimaryNames}
                value={formData.primaryName}
                onChange={(value) => setFormData({ ...formData, primaryName: value || '' })}
                required
              />
            </Group>
            <Text size="sm" c="dimmed">
              Contact will be created as: {formData.username}@{formData.primaryName}
            </Text>
            <TextInput
              label="Profile Name"
              placeholder="Enter profile display name"
              value={formData.profileName}
              onChange={(e) => setFormData({ ...formData, profileName: e.target.value })}
              required
            />
            <TextInput
              label="Profile Email"
              placeholder="<EMAIL>"
              value={formData.profileEmail}
              onChange={(e) => setFormData({ ...formData, profileEmail: e.target.value })}
              required
            />
            <Group justify="flex-end" mt="md">
              <Button variant="light" onClick={() => setCreateModalOpened(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleSubmitContact}
                disabled={!formData.username || !formData.primaryName || !formData.profileName || !formData.profileEmail}
              >
                Create Contact
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* Asset Creation Modal */}
        <AssetCreationForm
          opened={assetModalOpened}
          onClose={() => setAssetModalOpened(false)}
          onAssetCreated={handleAssetCreated}
        />

        {/* View Public Assets Modal */}
        <Modal
          opened={viewPublicModalOpened}
          onClose={() => setViewPublicModalOpened(false)}
          title="Public Assets Showcase"
          size="xl"
          centered
        >
          {ownedPrimaryNames.length > 0 && (
     <> SAmple </>
          )}
        </Modal>
      </Stack>
    </FullLayout>
  );
}
