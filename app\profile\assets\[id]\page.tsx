'use client';

import { useState, useEffect } from 'react';
import { useParams, notFound } from 'next/navigation';
import {
  Title,
  Text,
  Stack,
  Paper,
  Group,
  Badge,
  Image,
  Alert,
  LoadingOverlay,
  Select,
  TextInput,
  Grid,
  Card,
  ThemeIcon,
  Box,
  Button,
  Divider,
  Avatar,
  Modal,
  Anchor
} from '@mantine/core';
import {
  IconTrophy,
  IconSearch,
  IconFilter,
  IconCalendar,
  IconUser,
  IconPhoto,
  IconAlertCircle,
  IconExternalLink,
  IconArrowLeft
} from '@tabler/icons-react';
import { fetchData } from 'src/lib/supabase';
import FullLayout from 'src/components/layouts/FullLayout';
import { getImage, ImageData } from 'src/lib/common';
import classes from 'src/components/Card/contact.module.css';
import {
  AssetTransfer,
  getAssetTypeColor,
  ASSET_TYPES
} from 'src/lib/assets-utils';
import AssetDisplay from 'src/components/Assets/AssetDisplay';

interface AssetTransferWithAsset extends AssetTransfer {
  assets: {
    id: string;
    title: string;
    description: string | null;
    asset_type: 'Badge' | 'Certificate' | 'Ticket' | 'Coupon';
    image_url: string;
    issuer_odude_name: string;
    issuer_email: string;
    expiry_date: string | null;
    metadata: any;
    created_at: string;
  };
}

interface UserInfo {
  profile: string;
  email: string;
  images?: ImageData;
  description?: string;
}

export default function ProfileAssetsPage() {
  const params = useParams();
  const rawId = params?.id as string;
  const odudeName = rawId && rawId.includes('%') ? decodeURIComponent(rawId).toLowerCase() : rawId?.toLowerCase();

  const [assets, setAssets] = useState<AssetTransferWithAsset[]>([]);
  const [filteredAssets, setFilteredAssets] = useState<AssetTransferWithAsset[]>([]);
  const [loading, setLoading] = useState(true);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [issuerFilter, setIssuerFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('active');
  const [selectedAsset, setSelectedAsset] = useState<AssetTransferWithAsset | null>(null);
  const [detailModalOpened, setDetailModalOpened] = useState(false);

  useEffect(() => {
    if (odudeName) {
      fetchAssets();
      fetchUserInfo();
    }
  }, [odudeName]);

  useEffect(() => {
    filterAssets();
  }, [assets, searchQuery, typeFilter, issuerFilter, statusFilter]);

  const fetchUserInfo = async () => {
    if (!odudeName) return;

    try {
      const { data, error } = await fetchData('contact', {
        select: 'profile, email, images, description',
        filter: [{ column: 'name', value: odudeName.toLowerCase() }],
        single: true
      });

      if (!error && data) {
        const userData = Array.isArray(data) ? data[0] : data;
        setUserInfo(userData);
      }
    } catch (error) {
      console.error('Error fetching user info:', error);
    }
  };

  const handleAvatarClick = () => {
    window.location.href = `/profile/${odudeName}`;
  };

  const handleAssetClick = (asset: AssetTransferWithAsset) => {
    setSelectedAsset(asset);
    setDetailModalOpened(true);
  };

  const fetchAssets = async () => {
    if (!odudeName) return;

    try {
      setLoading(true);

      // Fetch approved transfers with asset details for the specific ODude name
      const { data, error } = await fetchData('asset_transfers', {
        select: `
          *,
          assets (
            id,
            title,
            description,
            asset_type,
            image_url,
            template_id,
            issuer_odude_name,
            issuer_email,
            expiry_date,
            metadata,
            created_at
          )
        `,
        filter: [
          { column: 'to_odude_name', value: odudeName },
          { column: 'status', value: 'approved' }
        ]
      });

      if (!error && data) {
        const assetsArray = Array.isArray(data) ? data : [data];
        setAssets(assetsArray);
      } else if (error) {
        console.error('Error fetching assets:', error);
      }
    } catch (error) {
      console.error('Error fetching assets:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterAssets = () => {
    let filtered = [...assets];

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(transfer =>
        transfer.assets.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        transfer.assets.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        transfer.assets.issuer_odude_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by asset type
    if (typeFilter) {
      filtered = filtered.filter(transfer => transfer.assets.asset_type === typeFilter);
    }

    // Filter by issuer
    if (issuerFilter) {
      filtered = filtered.filter(transfer => 
        transfer.assets.issuer_odude_name.toLowerCase().includes(issuerFilter.toLowerCase())
      );
    }

    // Filter by status (active/expired)
    if (statusFilter === 'active') {
      filtered = filtered.filter(transfer =>
        !transfer.assets?.expiry_date || new Date(transfer.assets.expiry_date) >= new Date()
      );
    } else if (statusFilter === 'expired') {
      filtered = filtered.filter(transfer =>
        transfer.assets?.expiry_date && new Date(transfer.assets.expiry_date) < new Date()
      );
    }

    setFilteredAssets(filtered);
  };

  const getUniqueIssuers = () => {
    const issuers = assets.map(transfer => transfer.assets.issuer_odude_name);
    return Array.from(new Set(issuers)).sort();
  };

  const getAssetStats = () => {
    const total = assets.length;
    const active = assets.filter(transfer =>
      !transfer.assets?.expiry_date || new Date(transfer.assets.expiry_date) >= new Date()
    ).length;
    const expired = total - active;
    const byType = ASSET_TYPES.reduce((acc, type) => {
      acc[type] = assets.filter(transfer => transfer.assets.asset_type === type).length;
      return acc;
    }, {} as Record<string, number>);

    return { total, active, expired, byType };
  };

  if (!odudeName) {
    notFound();
  }

  if (loading) {
    return (
      <FullLayout>
        <Paper withBorder p="md" style={{ position: 'relative', minHeight: 200 }}>
          <LoadingOverlay visible />
        </Paper>
      </FullLayout>
    );
  }

  const stats = getAssetStats();
  const uniqueIssuers = getUniqueIssuers();

  return (
    <FullLayout>
      <Stack gap="xl">
        {/* Header */}
        <Group justify="center" align="center">
          <Group>
            <Avatar
              src={getImage(userInfo?.images, 1)}
              size={80}
              radius={80}
              mx="auto"
              mt={-30}
              className={classes.avatar}
              onClick={handleAvatarClick}
              style={{ cursor: 'pointer' }}
            />

            <div>
              <Title order={2}>{odudeName}</Title>
              <Text c="dimmed" size="sm">Assets Showcase</Text>
            </div>
          </Group>
        </Group>

        {assets.length === 0 ? (
          <Paper withBorder p="xl" style={{ textAlign: 'center' }}>
            <IconPhoto size={48} color="gray" style={{ margin: '0 auto 16px' }} />
            <Text size="lg" fw={500} mb="xs">No Public Assets</Text>
            <Text c="dimmed">
              {odudeName} hasn't approved any assets for public display yet.
            </Text>
          </Paper>
        ) : (
          <>
            {/* Stats Overview */}
            <Grid>
              <Grid.Col span={3}>
                <Paper withBorder p="md" style={{ textAlign: 'center' }}>
                  <Text size="xl" fw={700} c="blue">{stats.total}</Text>
                  <Text size="sm" c="dimmed">Total Assets</Text>
                </Paper>
              </Grid.Col>
              <Grid.Col span={3}>
                <Paper withBorder p="md" style={{ textAlign: 'center' }}>
                  <Text size="xl" fw={700} c="green">{stats.active}</Text>
                  <Text size="sm" c="dimmed">Active</Text>
                </Paper>
              </Grid.Col>
              <Grid.Col span={3}>
                <Paper withBorder p="md" style={{ textAlign: 'center' }}>
                  <Text size="xl" fw={700} c="orange">{stats.expired}</Text>
                  <Text size="sm" c="dimmed">Expired</Text>
                </Paper>
              </Grid.Col>
              <Grid.Col span={3}>
                <Paper withBorder p="md" style={{ textAlign: 'center' }}>
                  <Text size="xl" fw={700} c="purple">{uniqueIssuers.length}</Text>
                  <Text size="sm" c="dimmed">Issuers</Text>
                </Paper>
              </Grid.Col>
            </Grid>

            {/* Asset Type Breakdown */}
            <Paper withBorder p="md">
              <Text fw={500} mb="md">Assets by Type</Text>
              <Grid>
                {ASSET_TYPES.map(type => (
                  <Grid.Col span={3} key={type}>
                    <Group justify="space-between">
                      <Badge color={getAssetTypeColor(type)} variant="light">
                        {type}
                      </Badge>
                      <Text fw={500}>{stats.byType[type] || 0}</Text>
                    </Group>
                  </Grid.Col>
                ))}
              </Grid>
            </Paper>

            {/* Filters */}
            <Paper withBorder p="md">
              <Text fw={500} mb="md">Filters</Text>
              <Grid>
                <Grid.Col span={3}>
                  <TextInput
                    placeholder="Search assets..."
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <Select
                    placeholder="Filter by type"
                    data={[
                      { value: '', label: 'All Types' },
                      ...ASSET_TYPES.map(type => ({ value: type, label: type }))
                    ]}
                    value={typeFilter}
                    onChange={(value) => setTypeFilter(value || '')}
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <Select
                    placeholder="Filter by issuer"
                    data={[
                      { value: '', label: 'All Issuers' },
                      ...uniqueIssuers.map(issuer => ({ value: issuer, label: issuer }))
                    ]}
                    value={issuerFilter}
                    onChange={(value) => setIssuerFilter(value || '')}
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <Select
                    placeholder="Filter by status"
                    data={[
                      { value: '', label: 'All Assets' },
                      { value: 'active', label: 'Active Only' },
                      { value: 'expired', label: 'Expired Only' }
                    ]}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                  />
                </Grid.Col>
              </Grid>
            </Paper>

            {/* Assets Grid */}
            {filteredAssets.length === 0 ? (
              <Alert icon={<IconAlertCircle size={16} />} color="blue">
                No assets match your current filters.
              </Alert>
            ) : (
              <Grid>
                {filteredAssets.map((transfer) => {
                  const expired = transfer.assets?.expiry_date && new Date(transfer.assets.expiry_date) < new Date();

                  return (
                    <Grid.Col span={4} key={transfer.id}>
                      <Card
                        withBorder
                        p="md"
                        style={{
                          opacity: expired ? 0.7 : 1,
                          height: '100%',
                          cursor: 'pointer',
                          transition: 'transform 0.2s ease'
                        }}
                        onClick={() => handleAssetClick(transfer)}
                        onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
                        onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
                      >
                        <Card.Section>
                          <AssetDisplay
                            imageUrl={transfer.assets.image_url}
                            templateId={transfer.assets.template_id}
                            title={transfer.assets.title}
                            width={300}
                            height={200}
                            fit="cover"
                          />
                        </Card.Section>

                        <Stack gap="sm" mt="md">
                          <Group justify="space-between" align="flex-start">
                            <div style={{ flex: 1 }}>
                              <Text fw={500} lineClamp={2}>{transfer.assets.title}</Text>
                              {transfer.assets.description && (
                                <Text size="sm" c="dimmed" lineClamp={2}>
                                  {transfer.assets.description}
                                </Text>
                              )}
                            </div>
                            <Badge color={getAssetTypeColor(transfer.assets.asset_type)} variant="light" size="sm">
                              {transfer.assets.asset_type}
                            </Badge>
                          </Group>

                          <Divider />

                          <Group justify="space-between" align="flex-start">
                            <div>
                              <Group gap="xs" mb="xs">
                                <IconUser size={14} />
                                <Text size="sm">Issued by: {transfer.assets.issuer_odude_name}</Text>
                              </Group>
                              <Group gap="xs" mb="xs">
                                <IconCalendar size={14} />
                                <Text size="sm">
                                  Issued: {new Date(transfer.assets.created_at).toLocaleDateString()}
                                </Text>
                              </Group>
                              {transfer.assets.expiry_date && (
                                <Group gap="xs">
                                  <IconCalendar size={14} />
                                  <Text size="sm" c={expired ? 'red' : 'dimmed'}>
                                    Expires: {new Date(transfer.assets.expiry_date).toLocaleDateString()}
                                  </Text>
                                </Group>
                              )}
                            </div>
                            {expired && <Badge color="red" size="xs">Expired</Badge>}
                          </Group>

                          <Button
                            variant="light"
                            size="xs"
                            leftSection={<IconExternalLink size={14} />}
                            onClick={() => window.open(`/profile/${transfer.assets.issuer_odude_name}`, '_blank')}
                          >
                            View Issuer Profile
                          </Button>
                        </Stack>
                      </Card>
                    </Grid.Col>
                  );
                })}
              </Grid>
            )}
          </>
        )}

        {/* Asset Detail Modal */}
        {selectedAsset && (
          <Modal
            opened={detailModalOpened}
            onClose={() => setDetailModalOpened(false)}
            title={selectedAsset.assets.title}
            size="lg"
            centered
          >
            <Stack gap="md">
              <AssetDisplay
                imageUrl={selectedAsset.assets.image_url}
                templateId={selectedAsset.assets.template_id}
                title={selectedAsset.assets.title}
                width={400}
                height={300}
                fit="contain"
              />

              <Group justify="space-between">
                <Badge color={getAssetTypeColor(selectedAsset.assets.asset_type)} variant="light">
                  {selectedAsset.assets.asset_type}
                </Badge>
                {selectedAsset.assets.expiry_date && new Date(selectedAsset.assets.expiry_date) < new Date() && (
                  <Badge color="red" size="sm">Expired</Badge>
                )}
              </Group>

              {selectedAsset.assets.description && (
                <Text>{selectedAsset.assets.description}</Text>
              )}

              <Divider />

              <Group gap="xs">
                <IconUser size={16} />
                <Text size="sm">Issued by: </Text>
                <Anchor
                  size="sm"
                  href={`/profile/${selectedAsset.assets.issuer_odude_name}`}
                  target="_blank"
                >
                  {selectedAsset.assets.issuer_odude_name}
                </Anchor>
              </Group>

              <Group gap="xs">
                <IconCalendar size={16} />
                <Text size="sm">
                  Issued: {new Date(selectedAsset.assets.created_at).toLocaleDateString()}
                </Text>
              </Group>

              {selectedAsset.assets.expiry_date && (
                <Group gap="xs">
                  <IconCalendar size={16} />
                  <Text size="sm" c={new Date(selectedAsset.assets.expiry_date) < new Date() ? 'red' : 'dimmed'}>
                    Expires: {new Date(selectedAsset.assets.expiry_date).toLocaleDateString()}
                  </Text>
                </Group>
              )}
            </Stack>
          </Modal>
        )}
      </Stack>


    </FullLayout>
  );
}
